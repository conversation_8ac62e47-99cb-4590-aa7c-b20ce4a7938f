import React from 'react';
import './MembersList.css';

const MembersList = ({ members }) => {
  if (!members || members.length === 0) {
    return null;
  }

  // 年齢を計算する関数
  const calculateAge = (birthDate) => {
    if (!birthDate) return null;
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  return (
    <div className="members-list">
      <h3 className="members-title">メンバー</h3>
      <div className="members-grid">
        {members.map((member, index) => (
          <div key={index} className="member-card">
            <div className="member-image-container">
              <img
                src={member.image}
                alt={member.name}
                className="member-image"
                onError={(e) => {
                  e.target.src = `https://via.placeholder.com/150x150?text=${encodeURIComponent(member.name)}`;
                }}
              />
            </div>
            <div className="member-info">
              <h4 className="member-name">{member.name}</h4>
              {member.role && (
                <span className="member-role">{member.role}</span>
              )}
              {member.bio && (
                <p className="member-bio">{member.bio}</p>
              )}

              {/* 出身地と生年月日 */}
              <div className="member-details">
                {member.birthPlace && (
                  <div className="member-detail-item">
                    <span className="detail-label">出身地:</span>
                    <span className="detail-value">{member.birthPlace}</span>
                  </div>
                )}
                {member.birthDate && (
                  <div className="member-detail-item">
                    <span className="detail-label">生年月日:</span>
                    <span className="detail-value">
                      {member.birthDate} ({calculateAge(member.birthDate)}歳)
                    </span>
                  </div>
                )}
                {/* 学歴情報 */}
                {member.education && (
                  <div className="education-section">
                    {typeof member.education === 'string' ? (
                      <div className="member-detail-item">
                        <span className="detail-label">最終学歴:</span>
                        <span className="detail-value">{member.education}</span>
                      </div>
                    ) : (
                      <>
                        {member.education.elementary && (
                          <div className="member-detail-item">
                            <span className="detail-label">小学校:</span>
                            <span className="detail-value">{member.education.elementary}</span>
                          </div>
                        )}
                        {member.education.junior && (
                          <div className="member-detail-item">
                            <span className="detail-label">中学校:</span>
                            <span className="detail-value">{member.education.junior}</span>
                          </div>
                        )}
                        {member.education.high && (
                          <div className="member-detail-item">
                            <span className="detail-label">高校:</span>
                            <span className="detail-value">{member.education.high}</span>
                          </div>
                        )}
                        {member.education.university && (
                          <div className="member-detail-item">
                            <span className="detail-label">最終学歴:</span>
                            <span className="detail-value">{member.education.university}</span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
              {member.socialLinks && member.socialLinks.length > 0 && (
                <div className="member-social-links">
                  <span className="social-label">個人SNS:</span>
                  <div className="social-buttons">
                    {member.socialLinks.map((link, linkIndex) => (
                      <a
                        key={linkIndex}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`social-button ${link.platform.toLowerCase()}`}
                        title={`${member.name}の${link.platform}`}
                      >
                        {link.platform}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MembersList;
