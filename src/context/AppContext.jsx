import React, { createContext, useContext, useState } from 'react';

// アプリの状態管理用Context
const AppContext = createContext();

// ダミーデータ
const initialData = {
  comedians: [
    {
      id: 1,
      name: "明石家さんま",
      image: "https://via.placeholder.com/200x200?text=さんま",
      bio: "関西お笑い界の重鎮。トーク番組の司会者としても活躍。",
      category: "関西芸人",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/sanma" },
        { platform: "Instagram", url: "https://instagram.com/sanma" }
      ]
    },
    {
      id: 2,
      name: "ダウンタウン",
      image: "https://via.placeholder.com/200x200?text=DT",
      bio: "松本人志と浜田雅功のコンビ。バラエティ番組で絶大な人気。",
      category: "漫才コンビ",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/downtown" }
      ],
      members: [
        {
          name: "松本人志",
          role: "ボケ",
          image: "/images/matsumoto-hitoshi.jpg",
          bio: "ダウンタウンのボケ担当。映画監督としても活動。",
          socialLinks: [
            { platform: "X", url: "https://twitter.com/matsu_bouzu" },
            { platform: "Instagram", url: "https://instagram.com/matsu_bouzu" }
          ]
        },
        {
          name: "浜田雅功",
          role: "ツッコミ",
          image: "/images/hamada-masatoshi.jpg",
          bio: "ダウンタウンのツッコミ担当。音楽活動も行う。",
          socialLinks: [
            { platform: "X", url: "https://twitter.com/hamada_masatoshi" },
            { platform: "Instagram", url: "https://instagram.com/hamada_masatoshi" }
          ]
        }
      ]
    },
    {
      id: 3,
      name: "有吉弘行",
      image: "https://via.placeholder.com/200x200?text=有吉",
      bio: "毒舌キャラで人気のお笑い芸人。ラジオパーソナリティとしても活動。",
      category: "ピン芸人",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/ariyoshi" },
        { platform: "YouTube", url: "https://youtube.com/ariyoshi" }
      ]
    },
    {
      id: 4,
      name: "令和ロマン",
      image: "/images/reiwa-roman.jpg",
      bio: "松井ケムリと髙比良 直樹のコンビ。独特な世界観とシュールなネタで人気。",
      category: "漫才コンビ",
      socialLinks: [
        { platform: "X", url: "https://x.com/official_reiwa" },
        { platform: "YouTube", url: "https://www.youtube.com/@official-reiwaroman" },
        { platform: "公式サイト", url: "https://www.reiwaroman-official.com" }
      ],
      formationYear: "2018年",
      activeYears: "2015年〜",
      formerName: "魔人無骨",
      contemporaries: "ヨネダ2000、サンタモニカ、ラランドなど",
      members: [
        {
          name: "松井ケムリ",
          role: "ボケ",
          image: "/images/matsui-kemuri.jpg",
          bio: "令和ロマンのボケ担当。独特な発想力で観客を魅了する。",
          birthDate: "1995-04-15",
          birthPlace: "大阪府",
          education: {
            elementary: "大阪市立住吉小学校",
            junior: "大阪市立住吉中学校",
            high: "大阪府立住吉高等学校",
            university: "大阪芸術大学芸術学部卒業"
          },
          socialLinks: [
            { platform: "X", url: "https://x.com/smoke_matsui" },
            { platform: "Instagram", url: "https://www.instagram.com/smoke_matsui/" }
          ]
        },
        {
          name: "髙比良 直樹",
          role: "ツッコミ",
          image: "/images/takahira-naoki.jpg",
          bio: "令和ロマンのツッコミ担当。冷静な突っ込みでコンビを支える。",
          birthDate: "1994-09-03",
          birthPlace: "東京都練馬区",
          education: {
            elementary: "練馬区立光が丘第八小学校",
            junior: "練馬区立光が丘第三中学校",
            high: "東京都立石神井高等学校",
            university: "慶應義塾大学文学部中退"
          },
          socialLinks: [
            { platform: "X", url: "https://x.com/Ku_ru_ma__" },
            { platform: "Instagram", url: "https://www.instagram.com/kuruma_t/" }
          ]
        }
      ]
    }
  ],
  events: [
    {
      id: 1,
      comedianId: 1,
      eventName: "さんまのお笑い向上委員会",
      date: "2024-12-15",
      venue: "フジテレビ",
      detailsURL: "https://example.com/event1",
      isApproved: true,
      submittedBy: "ファンA"
    },
    {
      id: 2,
      comedianId: 2,
      eventName: "ガキの使いやあらへんで",
      date: "2024-12-20",
      venue: "日本テレビ",
      detailsURL: "https://example.com/event2",
      isApproved: false,
      submittedBy: "ファンB"
    }
  ],
  articles: [
    {
      id: 1,
      comedianId: 1,
      title: "さんまさんの意外な一面",
      content: "先日のラジオで語られたエピソードが印象的でした。普段は明るいキャラクターで知られるさんまさんですが、実は読書家でもあり、哲学書なども愛読されているそうです。",
      imageURL: "https://via.placeholder.com/300x200?text=記事画像",
      isApproved: true,
      submittedBy: "ファンC"
    },
    {
      id: 2,
      comedianId: 3,
      title: "有吉さんの新番組について",
      content: "有吉さんの新しい深夜番組がスタートします。これまでとは違った一面を見せてくれる企画が盛りだくさんとのことで、ファンの間では大きな話題となっています。",
      imageURL: "https://via.placeholder.com/300x200?text=記事画像2",
      isApproved: false,
      submittedBy: "ファンD"
    }
  ]
};

// AppProviderコンポーネント
export function AppProvider({ children }) {
  const [data, setData] = useState(initialData);

  const updateData = (newData) => {
    setData(newData);
  };

  return (
    <AppContext.Provider value={{ data, updateData }}>
      {children}
    </AppContext.Provider>
  );
}

// カスタムフック
export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

export default AppContext;
